#!/usr/bin/env python3
"""
Performance Test Suite for HashSetSearch Algorithm

This module contains performance tests for the HashSetSearch algorithm,
measuring search speed across different file sizes and query patterns.

The tests evaluate:
- Search performance with various file sizes (10K to 250K lines)
- Reread mode performance characteristics
- Real-world query patterns and success rates
- Average search time measurements

Author: <PERSON>
Date: 2025
"""

import time
from search_algorithms_new import HashSetSearch


def test_search_speed():
    """
    Test HashSetSearch performance across different file sizes.

    Measures search performance using the reread mode of HashSetSearch
    across multiple file sizes with various test queries. Provides
    detailed timing information and success rate statistics.

    Test Configuration:
    - File sizes: 10K, 50K, 100K, 250K lines
    - Number of searches per file: 10
    - Query patterns: Mix of common and specific strings
    - Mode: Reread on query (worst-case performance)

    Output:
    - Individual search times for each query
    - Average search time per file size
    - Success rate (found vs not found queries)
    """
    test_files = [
        ("test_data/bench_10000.txt", "10K"),
        ("test_data/bench_50000.txt", "50K"),
        ("test_data/bench_100000.txt", "100K"),
        ("test_data/bench_250000.txt", "250K"),
    ]
    
    n_searches = 10
    test_words = ["test", "example", "benchmark", "13;0;1;26;0;7;3;0;", "speed"]
    
    for file_path, size_label in test_files:
        searcher = HashSetSearch(use_frozenset=True, reread_on_query=True)
        print(f"\nTesting HashSetSearch with file size {size_label}")
        print("-" * 50)
        
        total_time = 0
        found_count = 0
        
        # Test with different words for more realistic results
        for i in range(n_searches):
            word = test_words[i % len(test_words)]
            start = time.perf_counter()
            found = searcher.search(word, file_path)
            duration = (time.perf_counter() - start) * 1000
            if found:
                found_count += 1
            total_time += duration
            print(f"Search {i+1} ({word:12}): {duration:6.2f}ms {'[FOUND]' if found else ''}")
        
        avg_time = total_time / n_searches
        print("-" * 50)
        print(f"File size: {size_label}")
        print(f"Average search time: {avg_time:.2f}ms")
        print(f"Words found: {found_count}/{n_searches}")
        print("-" * 50)

if __name__ == "__main__":
    test_search_speed()