#!/usr/bin/env python3
"""
Comprehensive Test Suite for ServerConfig Class

This module contains unit tests for the ServerConfig class, ensuring
proper configuration file loading, validation, and error handling.

Test Coverage:
- Configuration file loading and parsing
- Parameter type validation (string, int, boolean)
- Required parameter validation
- Error handling for missing or invalid files
- Section-based parameter access

Author: <PERSON>
Date: 2025
"""

import pytest
from server import ServerConfig, ConfigurationError


def test_config_loading():
    """Test basic configuration loading and parameter type validation."""
    config = ServerConfig()
    assert config.file_path is not None
    assert isinstance(config.get('host', section='server'), str)
    assert isinstance(config.getint('port', section='server'), int)
    assert isinstance(config.getboolean('REREAD_ON_QUERY', section='DEFAULT'), bool)
    assert isinstance(config.get('ssl_enabled', section='ssl'), str)
    assert isinstance(config.get('log_level', section='logging'), str)

def test_invalid_config_file():
    """Test handling of invalid or non-existent config file."""
    with pytest.raises(ConfigurationError):
        ServerConfig("nonexistent.ini")

def test_required_parameters():
    """Test validation of required parameters across different config sections."""
    config = ServerConfig()
    # Test required parameters from DEFAULT section
    assert config.get('linuxpath') is not None
    assert isinstance(config.getboolean('REREAD_ON_QUERY'), bool)
    
    # Test required parameters from server section
    assert config.get('host', section='server') is not None
    assert config.getint('port', section='server') > 0
    assert config.getint('max_connections', section='server') > 0
