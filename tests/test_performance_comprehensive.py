#!/usr/bin/env python3
"""
Comprehensive Performance Test Suite for TCP String Search Server

This module provides extensive performance testing capabilities including:
- File size scaling tests (10K to 1M+ lines)
- REREAD_ON_QUERY mode performance comparison
- Memory usage and CPU utilization monitoring
- Query execution time analysis
- Performance degradation point identification

Author: <PERSON>
Date: 2025
"""

import os
import sys
import time
import psutil
import threading
import tempfile
import socket
from pathlib import Path
from typing import List, Dict, Any, Tuple, Optional
from unittest.mock import patch
import pytest

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from server import ServerConfig, FileSearchEngine, StringSearchServer
from client import SearchClient


class PerformanceMonitor:
    """Monitor system performance during tests."""
    
    def __init__(self):
        self.process = psutil.Process()
        self.monitoring = False
        self.measurements: List[Dict[str, float]] = []
        self._monitor_thread: Optional[threading.Thread] = None
    
    def start_monitoring(self, interval: float = 0.1) -> None:
        """Start performance monitoring."""
        self.monitoring = True
        self.measurements.clear()
        self._monitor_thread = threading.Thread(
            target=self._monitor_loop, args=(interval,))
        self._monitor_thread.daemon = True
        self._monitor_thread.start()
    
    def stop_monitoring(self) -> Dict[str, float]:
        """Stop monitoring and return statistics."""
        self.monitoring = False
        if self._monitor_thread:
            self._monitor_thread.join(timeout=1.0)
        
        if not self.measurements:
            return {
                'avg_cpu_percent': 0.0,
                'max_cpu_percent': 0.0,
                'avg_memory_mb': 0.0,
                'max_memory_mb': 0.0,
                'measurement_count': 0
            }
        
        cpu_values = [m['cpu_percent'] for m in self.measurements]
        memory_values = [m['memory_mb'] for m in self.measurements]
        
        return {
            'avg_cpu_percent': sum(cpu_values) / len(cpu_values),
            'max_cpu_percent': max(cpu_values),
            'avg_memory_mb': sum(memory_values) / len(memory_values),
            'max_memory_mb': max(memory_values),
            'measurement_count': len(self.measurements)
        }
    
    def _monitor_loop(self, interval: float) -> None:
        """Internal monitoring loop."""
        while self.monitoring:
            try:
                cpu_percent = self.process.cpu_percent()
                memory_info = self.process.memory_info()
                memory_mb = memory_info.rss / 1024 / 1024
                
                self.measurements.append({
                    'timestamp': time.time(),
                    'cpu_percent': cpu_percent,
                    'memory_mb': memory_mb
                })
                
                time.sleep(interval)
            except (psutil.NoSuchProcess, psutil.AccessDenied):
                break


def create_test_file(size_lines: int, temp_dir: Path) -> Path:
    """Create a test file with specified number of lines."""
    test_file = temp_dir / f"test_{size_lines}.txt"
    
    with open(test_file, 'w') as f:
        for i in range(size_lines):
            # Create varied content to make searches realistic
            if i % 1000 == 0:
                f.write(f"marker_line_{i}\n")
            elif i % 100 == 0:
                f.write(f"test_pattern_{i % 10}\n")
            else:
                f.write(f"line_{i:08d}_content_data\n")
    
    return test_file


def create_temp_config(temp_dir: Path, file_path: str, reread: bool = False) -> Path:
    """Create temporary configuration file."""
    config_file = temp_dir / "test_config.ini"
    
    config_content = f"""[DEFAULT]
linuxpath = {file_path}
reread_on_query = {str(reread).lower()}

[server]
host = localhost
port = 0
max_connections = 200
max_payload_size = 1024
connection_timeout = 5
tcp_nodelay = true
socket_buffer_size = 262144

[logging]
log_level = ERROR
"""
    
    config_file.write_text(config_content)
    return config_file


class TestPerformanceScaling:
    """Test performance scaling across different file sizes."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            yield Path(tmp_dir)
    
    def test_file_size_scaling_performance(self, temp_dir: Path):
        """Test performance scaling with increasing file sizes."""
        file_sizes = [10000, 50000, 100000, 250000, 500000]
        results = []
        
        for size in file_sizes:
            print(f"\nTesting file size: {size:,} lines")
            
            # Create test file
            test_file = create_test_file(size, temp_dir)
            
            # Test both modes
            for reread_mode in [False, True]:
                mode_name = "REREAD" if reread_mode else "CACHED"
                print(f"  Testing {mode_name} mode...")
                
                # Create config
                config_file = create_temp_config(temp_dir, str(test_file), reread_mode)
                
                # Initialize components
                config = ServerConfig(str(config_file))
                search_engine = FileSearchEngine(str(test_file), reread_mode)
                
                # Measure load time (file is loaded during initialization for cached mode)
                monitor = PerformanceMonitor()
                monitor.start_monitoring()

                load_start = time.perf_counter()
                # For cached mode, file is already loaded during initialization
                # For reread mode, we measure the first search which includes loading
                if reread_mode:
                    # First search includes file loading time
                    search_engine.search("dummy_query_for_timing")
                load_time = (time.perf_counter() - load_start) * 1000
                
                # Test search performance
                test_queries = [
                    f"marker_line_{size//2}",  # Existing line
                    "nonexistent_query_12345",  # Non-existing
                    f"test_pattern_{size % 10}",  # Pattern match
                ]
                
                search_times = []
                for query in test_queries:
                    for _ in range(3):  # Multiple iterations
                        start = time.perf_counter()
                        result = search_engine.search(query)
                        duration = (time.perf_counter() - start) * 1000
                        search_times.append(duration)
                
                perf_stats = monitor.stop_monitoring()
                
                result_data = {
                    'file_size': size,
                    'mode': mode_name,
                    'load_time_ms': load_time,
                    'avg_search_time_ms': sum(search_times) / len(search_times),
                    'min_search_time_ms': min(search_times),
                    'max_search_time_ms': max(search_times),
                    'avg_cpu_percent': perf_stats['avg_cpu_percent'],
                    'max_memory_mb': perf_stats['max_memory_mb']
                }
                
                results.append(result_data)
                
                print(f"    Load time: {load_time:.2f}ms")
                print(f"    Avg search: {result_data['avg_search_time_ms']:.2f}ms")
                print(f"    Memory: {result_data['max_memory_mb']:.1f}MB")
        
        # Verify performance requirements
        for result in results:
            if result['mode'] == 'REREAD':
                assert result['avg_search_time_ms'] <= 40.0, \
                    f"REREAD mode too slow: {result['avg_search_time_ms']:.2f}ms > 40ms"
            else:
                assert result['avg_search_time_ms'] <= 0.5, \
                    f"CACHED mode too slow: {result['avg_search_time_ms']:.2f}ms > 0.5ms"
        
        # Print summary
        print("\n" + "="*80)
        print("PERFORMANCE SCALING SUMMARY")
        print("="*80)
        for result in results:
            print(f"{result['file_size']:>8,} lines | {result['mode']:>6} | "
                  f"Load: {result['load_time_ms']:>8.2f}ms | "
                  f"Search: {result['avg_search_time_ms']:>6.3f}ms | "
                  f"Memory: {result['max_memory_mb']:>6.1f}MB")

    def test_memory_usage_scaling(self, temp_dir: Path):
        """Test memory usage scaling with file size."""
        file_sizes = [10000, 50000, 100000, 250000]
        memory_results = []

        for size in file_sizes:
            test_file = create_test_file(size, temp_dir)
            config_file = create_temp_config(temp_dir, str(test_file), False)

            # Measure memory before loading
            process = psutil.Process()
            memory_before = process.memory_info().rss / 1024 / 1024

            # Load file and measure memory (file is loaded during initialization)
            search_engine = FileSearchEngine(str(test_file), False)

            memory_after = process.memory_info().rss / 1024 / 1024
            memory_increase = memory_after - memory_before

            memory_results.append({
                'file_size': size,
                'memory_before_mb': memory_before,
                'memory_after_mb': memory_after,
                'memory_increase_mb': memory_increase,
                'memory_per_line_bytes': (memory_increase * 1024 * 1024) / size
            })

            print(f"File size: {size:,} lines, Memory increase: {memory_increase:.1f}MB, "
                  f"Per line: {memory_results[-1]['memory_per_line_bytes']:.1f} bytes")

        # Verify memory usage is reasonable
        for result in memory_results:
            # Memory per line should be reasonable (less than 1KB per line)
            assert result['memory_per_line_bytes'] < 1024, \
                f"Memory usage too high: {result['memory_per_line_bytes']:.1f} bytes per line"

    def test_large_file_performance(self, temp_dir: Path):
        """Test performance with very large files (1M lines)."""
        large_size = 1000000  # 1M lines
        print(f"\nTesting large file performance: {large_size:,} lines")

        # Create large test file
        test_file = create_test_file(large_size, temp_dir)
        config_file = create_temp_config(temp_dir, str(test_file), False)

        # Test with cached mode only (reread would be too slow)
        monitor = PerformanceMonitor()
        monitor.start_monitoring()

        search_engine = FileSearchEngine(str(test_file), False)

        # Measure load time (file is loaded during initialization)
        load_start = time.perf_counter()
        # File is already loaded during FileSearchEngine initialization
        load_time = (time.perf_counter() - load_start) * 1000

        # Test search performance
        test_queries = [
            f"marker_line_{large_size//2}",  # Middle of file
            "nonexistent_query_12345",      # Non-existing
            f"line_{large_size-1000:08d}_content_data",  # Near end
        ]

        search_times = []
        for query in test_queries:
            start = time.perf_counter()
            result = search_engine.search(query)
            duration = (time.perf_counter() - start) * 1000
            search_times.append(duration)
            print(f"  Query '{query[:20]}...': {duration:.3f}ms ({'FOUND' if result else 'NOT FOUND'})")

        perf_stats = monitor.stop_monitoring()
        avg_search_time = sum(search_times) / len(search_times)

        print(f"Large file results:")
        print(f"  Load time: {load_time:.2f}ms")
        print(f"  Average search time: {avg_search_time:.3f}ms")
        print(f"  Memory usage: {perf_stats['max_memory_mb']:.1f}MB")
        print(f"  CPU usage: {perf_stats['avg_cpu_percent']:.1f}%")

        # Verify performance is still reasonable for large files
        assert avg_search_time <= 1.0, f"Large file search too slow: {avg_search_time:.3f}ms > 1.0ms"
        assert perf_stats['max_memory_mb'] <= 2048, f"Memory usage too high: {perf_stats['max_memory_mb']:.1f}MB"

    def test_query_pattern_performance(self, temp_dir: Path):
        """Test performance with different query patterns."""
        size = 100000
        test_file = create_test_file(size, temp_dir)
        config_file = create_temp_config(temp_dir, str(test_file), False)

        search_engine = FileSearchEngine(str(test_file), False)
        # File is already loaded during initialization

        # Test different query patterns
        query_patterns = [
            ("Short query", "test"),
            ("Medium query", "test_pattern_5"),
            ("Long query", "line_00050000_content_data"),
            ("Very long query", "a" * 100),
            ("Special chars", "line_12345_content_data!@#$%"),
            ("Numbers only", "12345"),
            ("Non-ASCII", "tëst_pättërn"),
        ]

        results = []
        for pattern_name, query in query_patterns:
            times = []
            for _ in range(5):  # Multiple iterations
                start = time.perf_counter()
                found = search_engine.search(query)
                duration = (time.perf_counter() - start) * 1000
                times.append(duration)

            avg_time = sum(times) / len(times)
            results.append({
                'pattern': pattern_name,
                'query_length': len(query),
                'avg_time_ms': avg_time,
                'found': found
            })

            print(f"{pattern_name:>15}: {avg_time:.3f}ms (len={len(query):>3}) {'[FOUND]' if found else ''}")

        # Verify all queries complete reasonably fast
        for result in results:
            assert result['avg_time_ms'] <= 2.0, \
                f"Query pattern '{result['pattern']}' too slow: {result['avg_time_ms']:.3f}ms"


class TestPerformanceDegradation:
    """Test to identify performance degradation points."""

    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            yield Path(tmp_dir)

    def test_find_breaking_point(self, temp_dir: Path):
        """Find the point where server performance significantly degrades."""
        # Test progressively larger files until performance degrades
        base_sizes = [10000, 25000, 50000, 100000, 250000, 500000, 750000, 1000000]
        degradation_threshold = 5.0  # 5ms threshold for significant degradation

        previous_time = 0.0
        breaking_point = None

        print("\nFinding performance degradation point...")
        print("File Size    | Load Time | Search Time | Degradation")
        print("-" * 55)

        for size in base_sizes:
            try:
                test_file = create_test_file(size, temp_dir)
                config_file = create_temp_config(temp_dir, str(test_file), False)

                search_engine = FileSearchEngine(str(test_file), False)

                # Measure load time (file is loaded during initialization)
                load_start = time.perf_counter()
                # File is already loaded during FileSearchEngine initialization
                load_time = (time.perf_counter() - load_start) * 1000

                # Measure search time
                start = time.perf_counter()
                search_engine.search("nonexistent_query")
                search_time = (time.perf_counter() - start) * 1000

                # Check for degradation
                degradation = search_time - previous_time if previous_time > 0 else 0.0
                degradation_flag = "***" if degradation > degradation_threshold else ""

                print(f"{size:>8,} | {load_time:>8.2f}ms | {search_time:>9.3f}ms | "
                      f"{degradation:>+8.3f}ms {degradation_flag}")

                if degradation > degradation_threshold and breaking_point is None:
                    breaking_point = size
                    print(f"*** PERFORMANCE DEGRADATION DETECTED AT {size:,} LINES ***")

                previous_time = search_time

                # Stop if we hit memory limits or extreme slowness
                if load_time > 10000 or search_time > 100:  # 10s load or 100ms search
                    print(f"Stopping at {size:,} lines due to performance limits")
                    break

            except MemoryError:
                print(f"Memory limit reached at {size:,} lines")
                break
            except Exception as e:
                print(f"Error at {size:,} lines: {e}")
                break

        if breaking_point:
            print(f"\nPerformance degradation point: {breaking_point:,} lines")
        else:
            print("\nNo significant performance degradation detected in tested range")

        return breaking_point
