#!/usr/bin/env python3
"""
Comprehensive Exception Handling Test Suite for TCP String Search Server

This module tests all possible exception scenarios and error conditions:
- File operation errors (not found, permission denied, corrupted files)
- Network errors (connection timeouts, socket errors, SSL failures)
- Configuration errors (invalid parameters, missing sections)
- Resource exhaustion (memory limits, file descriptor limits)
- Malformed input handling (invalid queries, binary data, oversized payloads)

Author: <PERSON>
Date: 2025
"""

import os
import sys
import tempfile
import socket
import threading
import time
import stat
from pathlib import Path
from unittest.mock import patch, MagicMock
import pytest

# Add parent directory to path for imports
sys.path.insert(0, os.path.dirname(os.path.dirname(__file__)))

from server import (
    ServerConfig, FileSearchEngine, StringSearchServer, 
    ConfigurationError, FileSearchError, create_ssl_context
)
from client import SearchClient


class TestFileOperationExceptions:
    """Test file operation exception handling."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            yield Path(tmp_dir)
    
    def test_file_not_found_error(self, temp_dir: Path):
        """Test handling of non-existent files."""
        nonexistent_file = temp_dir / "nonexistent.txt"
        
        # Test FileSearchEngine with non-existent file
        with pytest.raises(FileSearchError, match="File not found"):
            FileSearchEngine(str(nonexistent_file))
    
    def test_file_permission_denied(self, temp_dir: Path):
        """Test handling of permission denied errors."""
        # Create a file and remove read permissions
        test_file = temp_dir / "no_permission.txt"
        test_file.write_text("test content\n")
        
        # Remove read permissions
        test_file.chmod(0o000)
        
        try:
            # Test FileSearchEngine with unreadable file
            with pytest.raises(FileSearchError, match="File not readable"):
                FileSearchEngine(str(test_file))
        finally:
            # Restore permissions for cleanup
            test_file.chmod(0o644)
    
    def test_corrupted_file_handling(self, temp_dir: Path):
        """Test handling of corrupted or binary files."""
        # Create a file with binary content
        binary_file = temp_dir / "binary.txt"
        with open(binary_file, 'wb') as f:
            f.write(b'\x00\x01\x02\x03\x04\x05\xFF\xFE\xFD')

        # Should raise UnicodeDecodeError for binary content
        with pytest.raises(UnicodeDecodeError):
            FileSearchEngine(str(binary_file))
    
    def test_empty_file_handling(self, temp_dir: Path):
        """Test handling of empty files."""
        empty_file = temp_dir / "empty.txt"
        empty_file.touch()
        
        search_engine = FileSearchEngine(str(empty_file))
        result = search_engine.search("anything")
        assert result is False
    
    def test_very_large_file_handling(self, temp_dir: Path):
        """Test handling of very large files."""
        large_file = temp_dir / "large.txt"
        
        # Create a moderately large file (1MB)
        with open(large_file, 'w') as f:
            for i in range(50000):
                f.write(f"line_{i:06d}_with_some_content_data\n")
        
        # Should handle large files without crashing
        search_engine = FileSearchEngine(str(large_file))
        result = search_engine.search("line_025000_with_some_content_data")
        assert isinstance(result, bool)
    
    def test_file_with_unicode_content(self, temp_dir: Path):
        """Test handling of files with Unicode content."""
        unicode_file = temp_dir / "unicode.txt"
        
        with open(unicode_file, 'w', encoding='utf-8') as f:
            f.write("English line\n")
            f.write("Français: café, naïve, résumé\n")
            f.write("Deutsch: Müller, Größe, weiß\n")
            f.write("Русский: привет, мир\n")
            f.write("中文: 你好世界\n")
            f.write("العربية: مرحبا بالعالم\n")
            f.write("Emoji: 🚀🌟💻🔥\n")
        
        search_engine = FileSearchEngine(str(unicode_file))
        
        # Test searching for Unicode content
        assert search_engine.search("Français: café, naïve, résumé") is True
        assert search_engine.search("中文: 你好世界") is True
        assert search_engine.search("Emoji: 🚀🌟💻🔥") is True
        assert search_engine.search("nonexistent") is False


class TestConfigurationExceptions:
    """Test configuration exception handling."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            yield Path(tmp_dir)
    
    def test_missing_config_file(self):
        """Test handling of missing configuration file."""
        with pytest.raises(ConfigurationError, match="Configuration file .* not found"):
            ServerConfig("nonexistent_config.ini")
    
    def test_malformed_config_file(self, temp_dir: Path):
        """Test handling of malformed configuration files."""
        malformed_config = temp_dir / "malformed.ini"
        malformed_config.write_text("this is not valid ini format [[[")
        
        with pytest.raises(ConfigurationError, match="Failed to parse configuration file"):
            ServerConfig(str(malformed_config))
    
    def test_missing_required_parameters(self, temp_dir: Path):
        """Test handling of missing required configuration parameters."""
        incomplete_config = temp_dir / "incomplete.ini"
        incomplete_config.write_text("""[DEFAULT]
# Missing linuxpath and other required parameters
host = localhost
""")
        
        with pytest.raises(ConfigurationError, match="Required parameter .* not found"):
            ServerConfig(str(incomplete_config))
    
    def test_invalid_parameter_types(self, temp_dir: Path):
        """Test handling of invalid parameter types."""
        invalid_config = temp_dir / "invalid.ini"
        invalid_config.write_text("""[DEFAULT]
linuxpath = /tmp/test.txt
reread_on_query = not_a_boolean
host = localhost
port = not_a_number
max_connections = also_not_a_number
""")
        
        with pytest.raises(ConfigurationError):
            ServerConfig(str(invalid_config))
    
    def test_config_with_nonexistent_file_path(self, temp_dir: Path):
        """Test configuration pointing to non-existent file."""
        config_file = temp_dir / "config.ini"
        config_file.write_text("""[DEFAULT]
linuxpath = /nonexistent/path/file.txt
reread_on_query = false
host = localhost
port = 8888
max_connections = 100
""")
        
        # Config should fail because file doesn't exist
        with pytest.raises(ConfigurationError, match="File not found"):
            ServerConfig(str(config_file))


class TestNetworkExceptions:
    """Test network-related exception handling."""
    
    def test_connection_timeout(self):
        """Test handling of connection timeouts."""
        # Try to connect to a non-responsive address
        client = SearchClient(host='************', port=12345, timeout=1.0)
        
        with pytest.raises(Exception):  # Should raise timeout or connection error
            client.search("test")
    
    def test_connection_refused(self):
        """Test handling of connection refused errors."""
        # Try to connect to a port that's not listening
        client = SearchClient(host='localhost', port=65432, timeout=1.0)
        
        with pytest.raises(Exception):  # Should raise connection refused error
            client.search("test")
    
    def test_invalid_hostname(self):
        """Test handling of invalid hostnames."""
        client = SearchClient(host='invalid.hostname.that.does.not.exist', port=8888, timeout=2.0)
        
        with pytest.raises(Exception):  # Should raise name resolution error
            client.search("test")
    
    def test_ssl_connection_errors(self):
        """Test SSL connection error handling."""
        # Try SSL connection to non-SSL server (should fail)
        client = SearchClient(host='google.com', port=80, use_ssl=True, timeout=2.0)
        
        with pytest.raises(Exception):  # Should raise SSL error
            client.search("test")
    
    @patch('socket.socket')
    def test_socket_creation_failure(self, mock_socket):
        """Test handling of socket creation failures."""
        mock_socket.side_effect = OSError("Socket creation failed")
        
        client = SearchClient()
        with pytest.raises(Exception):
            client.search("test")
    
    @patch('socket.socket')
    def test_send_failure(self, mock_socket_class):
        """Test handling of send operation failures."""
        mock_socket = MagicMock()
        mock_socket_class.return_value = mock_socket
        mock_socket.sendall.side_effect = OSError("Send failed")
        
        client = SearchClient()
        with pytest.raises(Exception):
            client.search("test")
    
    @patch('socket.socket')
    def test_receive_failure(self, mock_socket_class):
        """Test handling of receive operation failures."""
        mock_socket = MagicMock()
        mock_socket_class.return_value = mock_socket
        mock_socket.recv.side_effect = OSError("Receive failed")
        
        client = SearchClient()
        with pytest.raises(Exception):
            client.search("test")


class TestMalformedInputHandling:
    """Test handling of malformed input and edge cases."""
    
    @pytest.fixture
    def temp_dir(self):
        """Create temporary directory for test files."""
        with tempfile.TemporaryDirectory() as tmp_dir:
            yield Path(tmp_dir)
    
    @pytest.fixture
    def test_server(self, temp_dir: Path):
        """Create a test server for input testing."""
        # Create test file
        test_file = temp_dir / "test.txt"
        test_file.write_text("test line\nanother line\nthird line\n")
        
        # Create config
        config_file = temp_dir / "config.ini"
        config_file.write_text(f"""[DEFAULT]
linuxpath = {test_file}
reread_on_query = false
host = localhost
port = 0
max_connections = 10
max_payload_size = 1024
""")
        
        # Start server
        config = ServerConfig(str(config_file))
        search_engine = FileSearchEngine(str(test_file), False)
        server = StringSearchServer(("localhost", 0), config, search_engine)
        
        server_thread = threading.Thread(target=server.serve_forever)
        server_thread.daemon = True
        server_thread.start()
        
        time.sleep(0.1)  # Wait for server to start
        
        yield server.server_address[0], server.server_address[1]
        
        server.shutdown()
        server.server_close()
        server_thread.join(timeout=1)
    
    def test_oversized_query(self, test_server: tuple):
        """Test handling of oversized queries."""
        host, port = test_server
        
        # Create a very large query
        large_query = "a" * 10000  # Much larger than typical buffer
        
        client = SearchClient(host=host, port=port)
        
        # Should handle gracefully (either truncate or reject)
        try:
            response, exec_time = client.search(large_query)
            # If it succeeds, response should be valid
            assert response in ["STRING EXISTS", "STRING NOT FOUND"]
        except ValueError as e:
            # Or it should raise a clear error about query size
            assert "too long" in str(e).lower()
    
    def test_binary_query_data(self, test_server: tuple):
        """Test handling of binary data in queries."""
        host, port = test_server
        
        # Send binary data directly
        try:
            sock = socket.create_connection((host, port))
            binary_data = b'\x00\x01\x02\x03\x04\x05\xFF\xFE\xFD\n'
            sock.sendall(binary_data)
            response = sock.recv(1024)
            sock.close()
            
            # Should get some response, not crash
            assert len(response) > 0
        except Exception:
            # Connection might be closed, which is also acceptable
            pass
    
    def test_empty_query(self, test_server: tuple):
        """Test handling of empty queries."""
        host, port = test_server

        client = SearchClient(host=host, port=port, timeout=2.0)

        # Empty query might cause timeout or be handled gracefully
        try:
            response, exec_time = client.search("")
            # If it succeeds, response should be valid
            assert response in ["STRING EXISTS", "STRING NOT FOUND"]
        except (TimeoutError, ConnectionError):
            # Timeout is also acceptable for empty queries
            pass
    
    def test_query_with_newlines(self, test_server: tuple):
        """Test handling of queries containing newlines."""
        host, port = test_server
        
        client = SearchClient(host=host, port=port)
        
        # Query with embedded newlines
        query_with_newlines = "test\nline\nwith\nnewlines"
        response, exec_time = client.search(query_with_newlines)
        
        # Should handle gracefully
        assert response in ["STRING EXISTS", "STRING NOT FOUND"]
    
    def test_unicode_query_handling(self, test_server: tuple):
        """Test handling of Unicode queries."""
        host, port = test_server
        
        client = SearchClient(host=host, port=port)
        
        unicode_queries = [
            "café",
            "naïve",
            "résumé",
            "Müller",
            "привет",
            "你好",
            "🚀🌟💻",
        ]
        
        for query in unicode_queries:
            response, exec_time = client.search(query)
            assert response in ["STRING EXISTS", "STRING NOT FOUND"]
