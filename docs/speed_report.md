# Search Algorithm Performance Report

## Performance Summary

### Average Search Time by File Size

| Algorithm                     |   10K |   50K |   100K |   250K |
|:------------------------------|------:|------:|-------:|-------:|
| Binary Search (Deduplicated)  | 0.002 | 0.003 |  0.002 |  0.004 |
| Hash Set (FrozenSet)          | 0.001 | 0.001 |  0.002 |  0.001 |
| Hash Set (FrozenSet) (Reread) | 0.018 | 0.018 |  0.02  |  0.02  |
| Linear Search (Optimized)     | 1.206 | 4.799 | 10.272 | 17.17  |
| Memory-Mapped                 | 1.544 | 7.797 |  9.892 | 23.343 |
| Native Grep                   | 1.981 | 3.03  |  3.647 |  5.086 |

### Load Time by File Size

| Algorithm                     |   10K |    50K |   100K |    250K |
|:------------------------------|------:|-------:|-------:|--------:|
| Binary Search (Deduplicated)  | 6.869 | 25.414 | 47.487 | 121.593 |
| Hash Set (FrozenSet)          | 2.743 | 13.527 | 32.525 |  82.749 |
| Hash Set (FrozenSet) (Reread) | 2.864 | 13.705 | 48.908 |  85.276 |
| Linear Search (Optimized)     | 0.001 |  0.001 |  0.002 |   0.001 |
| Memory-Mapped                 | 0.07  |  0.116 |  0.142 |   0.354 |
| Native Grep                   | 0.005 |  0.001 |  0.001 |   0.001 |

## Performance Visualizations

### Load Time Comparison
![Load Time Comparison](load_time_(ms)_chart.png)

### Search Time Comparison
![Search Time Comparison](avg_search_time_(ms)_chart.png)

## Algorithm Characteristics

1. **HashSet (FrozenSet)**: O(1) lookup with memory trade-off
2. **HashSet (FrozenSet) (Reread)**: No initial memory overhead,                but slower search
3. **Linear Search**: Simple implementation,                 high time complexity (O(n))
4. **Binary Search**: O(log n) search with sorting overhead
5. **Memory-Mapped**: Efficient for large files
6. **Native Grep**: System-level optimization
