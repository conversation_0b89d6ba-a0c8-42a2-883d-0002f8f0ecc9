# Search Algorithm Performance Report

## Performance Summary

### Average Search Time by File Size

| Algorithm                     |   10K |    50K |   100K |   250K |
|:------------------------------|------:|-------:|-------:|-------:|
| Binary Search (Deduplicated)  | 0.004 |  0.005 |  0.004 |  0.005 |
| Hash Set (FrozenSet)          | 0.003 |  0.002 |  0.003 |  0.003 |
| Hash Set (FrozenSet) (Reread) | 0.118 |  0.079 |  0.043 |  0.042 |
| Linear Search (Optimized)     | 5.724 | 13.872 | 29.037 | 41.873 |
| Memory-Mapped                 | 4.295 | 20.838 | 26.924 | 46.834 |
| Native Grep                   | 7.312 |  7.924 |  8.324 | 19.102 |

### Load Time by File Size

| Algorithm                     |    10K |    50K |    100K |    250K |
|:------------------------------|-------:|-------:|--------:|--------:|
| Binary Search (Deduplicated)  | 20.455 | 70.325 | 130.082 | 322.833 |
| Hash Set (FrozenSet)          |  3.807 | 43.489 | 103.978 | 209.123 |
| Hash Set (FrozenSet) (Reread) |  8.435 | 45.027 | 121.918 | 198.542 |
| Linear Search (Optimized)     |  0.01  |  0.005 |   0.002 |   0.002 |
| Memory-Mapped                 |  0.133 |  0.178 |   0.224 |   0.333 |
| Native Grep                   |  0.003 |  0.002 |   0.002 |   0.011 |

## Performance Visualizations

### Load Time Comparison
![Load Time Comparison](load_time_(ms)_chart.png)

### Search Time Comparison
![Search Time Comparison](avg_search_time_(ms)_chart.png)

## Algorithm Characteristics

1. **HashSet (FrozenSet)**: O(1) lookup with memory trade-off
2. **HashSet (FrozenSet) (Reread)**: No initial memory overhead,                but slower search
3. **Linear Search**: Simple implementation,                 high time complexity (O(n))
4. **Binary Search**: O(log n) search with sorting overhead
5. **Memory-Mapped**: Efficient for large files
6. **Native Grep**: System-level optimization
