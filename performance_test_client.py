#!/usr/bin/env python3
"""
Dedicated Performance Test Client for TCP String Search Server

This client is specifically designed for performance benchmarking and load testing.
It provides advanced features for measuring server performance under various conditions.

Features:
- Concurrent connection testing
- Sustained load testing
- Performance metrics collection
- Resource usage monitoring
- Detailed timing analysis

Author: <PERSON>
Date: 2025
"""

import argparse
import socket
import ssl
import time
import threading
import statistics
import sys
from concurrent.futures import ThreadPoolExecutor, as_completed
from typing import List, Dict, Any, Tuple, Optional
import json


class PerformanceTestClient:
    """Advanced performance testing client for the TCP String Search Server."""
    
    def __init__(self, host: str = 'localhost', port: int = 8888,
                 use_ssl: bool = False, timeout: float = 10.0):
        """
        Initialize performance test client.
        
        Args:
            host: Server hostname or IP address
            port: Server port number
            use_ssl: Whether to use SSL connection
            timeout: Connection timeout in seconds
        """
        self.host = host
        self.port = port
        self.use_ssl = use_ssl
        self.timeout = timeout
        self.results: List[Dict[str, Any]] = []
    
    def create_connection(self) -> socket.socket:
        """Create a connection to the server."""
        sock = socket.socket(socket.AF_INET, socket.SOCK_STREAM)
        sock.settimeout(self.timeout)
        
        if self.use_ssl:
            context = ssl.create_default_context()
            context.check_hostname = False
            context.verify_mode = ssl.CERT_NONE
            sock = context.wrap_socket(sock, server_hostname=self.host)
        
        sock.connect((self.host, self.port))
        return sock
    
    def single_query(self, query: str) -> Tuple[str, float, bool]:
        """
        Execute a single query and measure performance.
        
        Returns:
            Tuple of (response, execution_time_ms, success)
        """
        start_time = time.perf_counter()
        
        try:
            sock = self.create_connection()
            
            # Send query
            query_bytes = query.encode('utf-8')
            if len(query_bytes) > 1024:
                raise ValueError(f"Query too long: {len(query_bytes)} bytes")
            
            sock.sendall(query_bytes + b'\n')
            
            # Receive response
            response = sock.recv(1024).decode('utf-8').strip()
            
            execution_time = (time.perf_counter() - start_time) * 1000
            
            sock.close()
            
            success = response in ["STRING EXISTS", "STRING NOT FOUND"]
            return response, execution_time, success
            
        except Exception as e:
            execution_time = (time.perf_counter() - start_time) * 1000
            return str(e), execution_time, False
    
    def benchmark_queries(self, queries: List[str], iterations: int = 10) -> Dict[str, Any]:
        """
        Benchmark a list of queries with multiple iterations.
        
        Args:
            queries: List of queries to test
            iterations: Number of iterations per query
            
        Returns:
            Dictionary with benchmark results
        """
        print(f"Benchmarking {len(queries)} queries with {iterations} iterations each...")
        
        all_times = []
        successful_queries = 0
        failed_queries = 0
        query_results = []
        
        for i, query in enumerate(queries):
            print(f"Testing query {i+1}/{len(queries)}: '{query[:50]}{'...' if len(query) > 50 else ''}'")
            
            query_times = []
            query_successes = 0
            
            for iteration in range(iterations):
                response, exec_time, success = self.single_query(query)
                query_times.append(exec_time)
                
                if success:
                    query_successes += 1
                    successful_queries += 1
                else:
                    failed_queries += 1
                
                # Small delay between iterations
                time.sleep(0.01)
            
            all_times.extend(query_times)
            
            query_result = {
                'query': query,
                'iterations': iterations,
                'successful_iterations': query_successes,
                'avg_time_ms': statistics.mean(query_times),
                'min_time_ms': min(query_times),
                'max_time_ms': max(query_times),
                'median_time_ms': statistics.median(query_times)
            }
            
            query_results.append(query_result)
            
            print(f"  Avg: {query_result['avg_time_ms']:.2f}ms, "
                  f"Min: {query_result['min_time_ms']:.2f}ms, "
                  f"Max: {query_result['max_time_ms']:.2f}ms, "
                  f"Success: {query_successes}/{iterations}")
        
        # Calculate overall statistics
        if all_times:
            overall_stats = {
                'total_queries': len(queries) * iterations,
                'successful_queries': successful_queries,
                'failed_queries': failed_queries,
                'success_rate_percent': (successful_queries / (len(queries) * iterations)) * 100,
                'avg_response_time_ms': statistics.mean(all_times),
                'min_response_time_ms': min(all_times),
                'max_response_time_ms': max(all_times),
                'median_response_time_ms': statistics.median(all_times),
                'p95_response_time_ms': statistics.quantiles(all_times, n=20)[18] if len(all_times) >= 20 else max(all_times),
                'p99_response_time_ms': statistics.quantiles(all_times, n=100)[98] if len(all_times) >= 100 else max(all_times)
            }
        else:
            overall_stats = {
                'total_queries': 0,
                'successful_queries': 0,
                'failed_queries': 0,
                'success_rate_percent': 0.0,
                'avg_response_time_ms': 0.0,
                'min_response_time_ms': 0.0,
                'max_response_time_ms': 0.0,
                'median_response_time_ms': 0.0,
                'p95_response_time_ms': 0.0,
                'p99_response_time_ms': 0.0
            }
        
        return {
            'overall_stats': overall_stats,
            'query_results': query_results
        }
    
    def concurrent_load_test(self, queries: List[str], concurrent_clients: int, 
                           duration_seconds: int) -> Dict[str, Any]:
        """
        Perform concurrent load testing.
        
        Args:
            queries: List of queries to use for testing
            concurrent_clients: Number of concurrent client threads
            duration_seconds: Duration of the test in seconds
            
        Returns:
            Dictionary with load test results
        """
        print(f"Starting concurrent load test:")
        print(f"  Concurrent clients: {concurrent_clients}")
        print(f"  Duration: {duration_seconds} seconds")
        print(f"  Queries: {len(queries)}")
        
        results_lock = threading.Lock()
        all_results = []
        
        def worker_thread(worker_id: int) -> None:
            """Worker thread for load testing."""
            worker_results = []
            start_time = time.time()
            query_index = 0
            
            while time.time() - start_time < duration_seconds:
                query = queries[query_index % len(queries)]
                response, exec_time, success = self.single_query(query)
                
                worker_results.append({
                    'worker_id': worker_id,
                    'query': query,
                    'response_time_ms': exec_time,
                    'success': success,
                    'timestamp': time.time()
                })
                
                query_index += 1
                
                # Small delay to prevent overwhelming the server
                time.sleep(0.001)
            
            with results_lock:
                all_results.extend(worker_results)
        
        # Start worker threads
        start_time = time.time()
        threads = []
        
        for i in range(concurrent_clients):
            thread = threading.Thread(target=worker_thread, args=(i,))
            thread.start()
            threads.append(thread)
        
        # Wait for all threads to complete
        for thread in threads:
            thread.join()
        
        end_time = time.time()
        actual_duration = end_time - start_time
        
        # Analyze results
        if all_results:
            successful_requests = sum(1 for r in all_results if r['success'])
            failed_requests = len(all_results) - successful_requests
            response_times = [r['response_time_ms'] for r in all_results if r['success']]
            
            if response_times:
                stats = {
                    'total_requests': len(all_results),
                    'successful_requests': successful_requests,
                    'failed_requests': failed_requests,
                    'success_rate_percent': (successful_requests / len(all_results)) * 100,
                    'actual_duration_seconds': actual_duration,
                    'queries_per_second': len(all_results) / actual_duration,
                    'successful_qps': successful_requests / actual_duration,
                    'avg_response_time_ms': statistics.mean(response_times),
                    'min_response_time_ms': min(response_times),
                    'max_response_time_ms': max(response_times),
                    'median_response_time_ms': statistics.median(response_times),
                    'p95_response_time_ms': statistics.quantiles(response_times, n=20)[18] if len(response_times) >= 20 else max(response_times),
                    'p99_response_time_ms': statistics.quantiles(response_times, n=100)[98] if len(response_times) >= 100 else max(response_times)
                }
            else:
                stats = {
                    'total_requests': len(all_results),
                    'successful_requests': 0,
                    'failed_requests': len(all_results),
                    'success_rate_percent': 0.0,
                    'actual_duration_seconds': actual_duration,
                    'queries_per_second': 0.0,
                    'successful_qps': 0.0,
                    'avg_response_time_ms': 0.0,
                    'min_response_time_ms': 0.0,
                    'max_response_time_ms': 0.0,
                    'median_response_time_ms': 0.0,
                    'p95_response_time_ms': 0.0,
                    'p99_response_time_ms': 0.0
                }
        else:
            stats = {
                'total_requests': 0,
                'successful_requests': 0,
                'failed_requests': 0,
                'success_rate_percent': 0.0,
                'actual_duration_seconds': actual_duration,
                'queries_per_second': 0.0,
                'successful_qps': 0.0,
                'avg_response_time_ms': 0.0,
                'min_response_time_ms': 0.0,
                'max_response_time_ms': 0.0,
                'median_response_time_ms': 0.0,
                'p95_response_time_ms': 0.0,
                'p99_response_time_ms': 0.0
            }
        
        print(f"\nLoad Test Results:")
        print(f"  Total requests: {stats['total_requests']:,}")
        print(f"  Successful requests: {stats['successful_requests']:,}")
        print(f"  Failed requests: {stats['failed_requests']:,}")
        print(f"  Success rate: {stats['success_rate_percent']:.1f}%")
        print(f"  QPS: {stats['queries_per_second']:.1f}")
        print(f"  Successful QPS: {stats['successful_qps']:.1f}")
        print(f"  Avg response time: {stats['avg_response_time_ms']:.2f}ms")
        print(f"  P95 response time: {stats['p95_response_time_ms']:.2f}ms")
        print(f"  P99 response time: {stats['p99_response_time_ms']:.2f}ms")
        
        return {
            'stats': stats,
            'raw_results': all_results
        }

    def test_connection(self) -> bool:
        """Test if the server is reachable."""
        try:
            response, exec_time, success = self.single_query("test_connection")
            return success
        except Exception:
            return False


def create_test_queries() -> List[str]:
    """Create a set of test queries for performance testing."""
    return [
        # Common patterns that might exist
        "test",
        "example",
        "data",
        "line",
        "content",

        # Specific patterns
        "line_001000_data",
        "marker_line_500",
        "test_pattern_5",

        # Non-existing patterns
        "nonexistent_query_12345",
        "this_should_not_exist",
        "missing_data_pattern",

        # Edge cases
        "",  # Empty query
        "a" * 100,  # Long query
        "special!@#$%^&*()chars",  # Special characters
        "unicode_tëst_pättërn",  # Unicode characters
    ]


def main():
    """Main function for command-line interface."""
    parser = argparse.ArgumentParser(
        description="Performance Test Client for TCP String Search Server",
        formatter_class=argparse.RawDescriptionHelpFormatter,
        epilog="""
Examples:
  # Basic connection test
  python3 performance_test_client.py --test

  # Benchmark with default queries
  python3 performance_test_client.py --benchmark

  # Custom benchmark
  python3 performance_test_client.py --benchmark --queries "test,example,data" --iterations 20

  # Load test with 50 concurrent clients for 60 seconds
  python3 performance_test_client.py --load-test --clients 50 --duration 60

  # SSL connection test
  python3 performance_test_client.py --ssl --test
        """
    )

    parser.add_argument('--host', default='localhost',
                       help='Server hostname (default: localhost)')
    parser.add_argument('--port', type=int, default=8888,
                       help='Server port (default: 8888)')
    parser.add_argument('--ssl', action='store_true',
                       help='Use SSL connection')
    parser.add_argument('--timeout', type=float, default=10.0,
                       help='Connection timeout in seconds (default: 10.0)')

    # Test modes
    parser.add_argument('--test', action='store_true',
                       help='Test server connectivity')
    parser.add_argument('--benchmark', action='store_true',
                       help='Run benchmark test')
    parser.add_argument('--load-test', action='store_true',
                       help='Run load test')

    # Benchmark options
    parser.add_argument('--queries', type=str,
                       help='Comma-separated list of queries for testing')
    parser.add_argument('--iterations', type=int, default=10,
                       help='Number of iterations per query (default: 10)')

    # Load test options
    parser.add_argument('--clients', type=int, default=10,
                       help='Number of concurrent clients for load test (default: 10)')
    parser.add_argument('--duration', type=int, default=30,
                       help='Load test duration in seconds (default: 30)')

    # Output options
    parser.add_argument('--output', type=str,
                       help='Save results to JSON file')
    parser.add_argument('--verbose', action='store_true',
                       help='Verbose output')

    args = parser.parse_args()

    # Create client
    client = PerformanceTestClient(
        host=args.host,
        port=args.port,
        use_ssl=args.ssl,
        timeout=args.timeout
    )

    results = {}

    # Test connection
    if args.test:
        print(f"Testing connection to {args.host}:{args.port}...")
        if client.test_connection():
            print("✓ Server is reachable")
            results['connection_test'] = {'success': True}
        else:
            print("✗ Server is not reachable")
            results['connection_test'] = {'success': False}
            sys.exit(1)

    # Benchmark test
    if args.benchmark:
        if args.queries:
            queries = [q.strip() for q in args.queries.split(',')]
        else:
            queries = create_test_queries()

        print(f"\nRunning benchmark test...")
        benchmark_results = client.benchmark_queries(queries, args.iterations)
        results['benchmark'] = benchmark_results

        # Print summary
        stats = benchmark_results['overall_stats']
        print(f"\nBenchmark Summary:")
        print(f"  Total queries: {stats['total_queries']:,}")
        print(f"  Success rate: {stats['success_rate_percent']:.1f}%")
        print(f"  Average response time: {stats['avg_response_time_ms']:.2f}ms")
        print(f"  Median response time: {stats['median_response_time_ms']:.2f}ms")
        print(f"  P95 response time: {stats['p95_response_time_ms']:.2f}ms")
        print(f"  P99 response time: {stats['p99_response_time_ms']:.2f}ms")

    # Load test
    if args.load_test:
        if args.queries:
            queries = [q.strip() for q in args.queries.split(',')]
        else:
            queries = create_test_queries()[:8]  # Use subset for load testing

        print(f"\nRunning load test...")
        load_results = client.concurrent_load_test(queries, args.clients, args.duration)
        results['load_test'] = load_results

    # Save results to file if requested
    if args.output:
        with open(args.output, 'w') as f:
            json.dump(results, f, indent=2)
        print(f"\nResults saved to {args.output}")

    # Print verbose results if requested
    if args.verbose and 'benchmark' in results:
        print(f"\nDetailed Query Results:")
        for query_result in results['benchmark']['query_results']:
            print(f"  Query: '{query_result['query'][:50]}{'...' if len(query_result['query']) > 50 else ''}'")
            print(f"    Avg: {query_result['avg_time_ms']:.2f}ms")
            print(f"    Min: {query_result['min_time_ms']:.2f}ms")
            print(f"    Max: {query_result['max_time_ms']:.2f}ms")
            print(f"    Success: {query_result['successful_iterations']}/{query_result['iterations']}")


if __name__ == "__main__":
    main()
